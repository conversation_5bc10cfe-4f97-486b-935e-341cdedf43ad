version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: n8n_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n_db
      POSTGRES_USER: n8n_user
      POSTGRES_PASSWORD: super_tajne_haslo
    volumes:
      - n8n_postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 256M
    ports:
      - "5433:5432"  # Zmiana portu, by nie k<PERSON><PERSON>wa<PERSON> z główną bazą PostgreSQL

  n8n:
    image: n8nio/n8n
    container_name: n8n
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - N8N_HOST=n8n.simetria.pl
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - N8N_SSL_PORT=443
      - WEBHOOK_URL=https://n8n.simetria.pl
      - N8N_ALLOW_EXTERNAL_ACCESS=true
      - N8N_SECURE_COOKIE=true
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_db
      - DB_POSTGRESDB_USER=n8n_user
      - DB_POSTGRESDB_PASSWORD=super_tajne_haslo   
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n

volumes:
  n8n_postgres_data:
  n8n_data:
