import json
import os
from google.oauth2 import service_account
from googleapiclient.discovery import build
from dotenv import load_dotenv
from datetime import datetime
import calendar

# Określenie ścieżki do pliku .env i credentials.json
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env_path = os.path.join(current_dir, 'config', '.env')
credentials_path = os.path.join(current_dir, 'config', 'credentials.json')

load_dotenv(env_path)

# Ładowanie zmiennych środowiskowych z config/.env
CREDENTIALS_FILE = os.getenv('GOOGLE_SHEETS_CREDENTIALS', 'credentials.json')
SPREADSHEET_ID = "12KTQOg92BLH49fDsFityPayaA0W8hXLXg67-t93y37Q"

class SheetUpdater:
    def __init__(self):
        # Wczytanie konfiguracji
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        config_path = os.path.join(current_dir, 'config', 'sheet_config.json')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Błąd podczas wczytywania konfiguracji: {e}")
            self.config = None
            raise Exception("Nie można wczytać konfiguracji arkusza")

        # Inicjalizacja Google Sheets API
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
        creds = service_account.Credentials.from_service_account_file(
            credentials_path, scopes=SCOPES)
        self.service = build('sheets', 'v4', credentials=creds)
        
        # Pobranie ID arkusza
        self.spreadsheet_id = os.getenv('GOOGLE_SPREADSHEET_ID')
        if not self.spreadsheet_id:
            raise Exception("Brak ID arkusza w zmiennych środowiskowych")

    def update_monthly_report(self, sales_data, cash_data, apaczka_data=None):
        try:
            # 1. Aktualizacja głównych danych
            success = self._update_main_values(sales_data)
            if not success:
                raise Exception("Błąd podczas aktualizacji głównych danych")

            # 2. Aktualizacja faktur gotówkowych
            if cash_data and 'invoices' in cash_data:
                success = self._update_cash_invoices(cash_data)
                if not success:
                    raise Exception("Błąd podczas aktualizacji faktur gotówkowych")

            # 3. Aktualizacja danych z Apaczki
            if apaczka_data:
                success = self._update_apaczka_data(apaczka_data)
                if not success:
                    raise Exception("Błąd podczas aktualizacji danych z Apaczki")

            # 4. Próba dodania komentarzy (opcjonalne)
            try:
                self._add_invoice_comments(cash_data)
            except Exception as comment_error:
                print(f"Uwaga: Nie udało się dodać komentarzy: {comment_error}")
                print("Kontynuuję działanie programu...")

            print("Arkusz został zaktualizowany pomyślnie!")
            return True

        except Exception as e:
            print(f"Błąd podczas aktualizacji arkusza: {e}")
            return False

    def _update_main_values(self, sales_data):
        """Aktualizuje główne wartości w arkuszu"""
        try:
            # Przygotowanie nazwy miesiąca
            date_from = datetime.strptime(sales_data['date_from'], '%Y-%m-%d')
            month_name = calendar.month_name[date_from.month].upper()
            year = date_from.year
            month_year = f"{month_name} {year}"

            updates = []
            
            # Aktualizacja statycznych komórek na podstawie konfiguracji
            for key, config in self.config['static_cells'].items():
                cell = config['cell']
                if config.get('type') == 'month_name':
                    value = month_year
                else:
                    # Pobieranie wartości po ścieżce ze źródła
                    source_path = config['source'].split('.')
                    value = sales_data
                    for path_element in source_path:
                        value = value.get(path_element, {})
                    if not isinstance(value, (int, float)):
                        value = 0

                updates.append({
                    'range': cell,
                    'values': [[value]]
                })

            # Aktualizacja kategorii
            for category_name, category_config in self.config['categories_mapping'].items():
                if category_config.get('cell'):  # tylko jeśli komórka jest przypisana
                    value = sales_data['categories'].get(category_name, {}).get('value', 0)
                    updates.append({
                        'range': category_config['cell'],
                        'values': [[value]]
                    })

            # Wykonanie aktualizacji
            for update in updates:
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=update['range'],
                    valueInputOption='RAW',
                    body={'values': update['values']}
                ).execute()

            return True
        except Exception as e:
            print(f"Błąd podczas aktualizacji głównych wartości: {e}")
            return False

    def _update_cash_invoices(self, cash_data):
        """Aktualizuje dane faktur gotówkowych"""
        try:
            start_row = 30
            for i, invoice in enumerate(cash_data['invoices']):
                row = start_row + i
                # Numer faktury
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=f'E{row}',
                    valueInputOption='RAW',
                    body={'values': [[invoice['number']]]}
                ).execute()
                
                # Wartość brutto
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=f'F{row}',
                    valueInputOption='RAW',
                    body={'values': [[invoice['price_gross']]]}
                ).execute()

            return True
        except Exception as e:
            print(f"Błąd podczas aktualizacji faktur gotówkowych: {e}")
            return False

    def _update_apaczka_data(self, apaczka_data):
        """Aktualizuje dane z Apaczki w arkuszu"""
        try:
            # Aktualizacja podsumowania
            for key, config in self.config['apaczka_data']['summary'].items():
                cell = config['cell']
                source_path = config['source'].split('.')
                value = apaczka_data
                for path_element in source_path:
                    value = value.get(path_element, 0)
                
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=cell,
                    valueInputOption='RAW',
                    body={'values': [[value]]}
                ).execute()

            # Aktualizacja listy przesyłek
            if 'shipments' in apaczka_data:
                start_row = int(self.config['apaczka_data']['shipments_list']['start_cell'][1:])
                columns = self.config['apaczka_data']['shipments_list']['columns']
                
                for i, shipment in enumerate(apaczka_data['shipments']):
                    row = start_row + i
                    
                    # Numer przesyłki
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{columns['number']}{row}",
                        valueInputOption='RAW',
                        body={'values': [[shipment['number']]]}
                    ).execute()
                    
                    # Koszt przesyłki
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{columns['cost']}{row}",
                        valueInputOption='RAW',
                        body={'values': [[shipment['cost']]]}
                    ).execute()

            return True
        except Exception as e:
            print(f"Błąd podczas aktualizacji danych z Apaczki: {e}")
            return False

    def _add_invoice_comments(self, cash_data):
        """Próbuje dodać komentarze do faktur (funkcja opcjonalna)"""
        if not cash_data or 'invoices' not in cash_data:
            return

        try:
            requests = []
            start_row = 30
            
            for i, invoice in enumerate(cash_data['invoices']):
                row = start_row + i
                comment = f"Odbiorca: {invoice['buyer_name']}\nData: {invoice['issue_date']}"
                
                requests.append({
                    'repeatCell': {
                        'range': {
                            'sheetId': 0,
                            'startRowIndex': row - 1,
                            'endRowIndex': row,
                            'startColumnIndex': 4,
                            'endColumnIndex': 5
                        },
                        'cell': {
                            'note': comment
                        },
                        'fields': 'note'
                    }
                })
            
            if requests:
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': requests}
                ).execute()
                print("Dodano komentarze do faktur gotówkowych")

        except Exception as e:
            raise Exception(f"Błąd podczas dodawania komentarzy: {e}")

    def _column_letter_to_number(self, column_letter):
        """Konwertuje literę kolumny na numer (A=0, B=1, etc.)"""
        result = 0
        for i, letter in enumerate(column_letter):
            result *= 26
            result += ord(letter.upper()) - ord('A')
        return result

    def clear_sheet(self):
        """Czyści cały arkusz"""
        try:
            self.service.spreadsheets().values().clear(
                spreadsheetId=SPREADSHEET_ID,
                range='A1:Z1000',
                body={}
            ).execute()
            print("Arkusz został wyczyszczony")
            return True
        except Exception as e:
            print(f"Błąd podczas czyszczenia arkusza: {e}")
            return False

def update_spreadsheet(sales_data, cash_data):
    """Funkcja pomocnicza do aktualizacji arkusza"""
    updater = SheetUpdater()
    return updater.update_monthly_report(sales_data, cash_data)

def clear_spreadsheet():
    """Funkcja pomocnicza do czyszczenia arkusza"""
    updater = SheetUpdater()
    return updater.clear_sheet()

if __name__ == "__main__":
    # Dane testowe
    test_sales_data = {
        'date_from': '2024-02-01',
        'date_to': '2024-02-29',
        'total': 1000.00,
        'grot': {'value': 200.00},
        'categories': {
            'Kategoria 1': {'value': 300.00},
            'Kategoria 2': {'value': 500.00}
        }
    }
    
    test_cash_data = {
        'total': 150.00
    }
    
    print("Test połączenia z Google Sheets...")
    updater = SheetUpdater()
    
    # Najpierw czyścimy arkusz
    print("\nCzyszczenie arkusza...")
    if not updater.clear_sheet():
        print("Nie udało się wyczyścić arkusza!")
        exit(1)
    
    print("\nPróba dodania danych testowych...")
    result = updater.update_monthly_report(test_sales_data, test_cash_data)
    
    if result:
        print("Test zakończony sukcesem!")
    else:
        print("Test nie powiódł się!")
