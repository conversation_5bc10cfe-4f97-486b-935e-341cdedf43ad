import json
import os
from datetime import datetime
from .sales_report import get_categories

def ensure_config_directory():
    """Sprawdza i tworzy katalog config jeśli nie istnieje"""
    current_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    config_dir = os.path.join(current_dir, 'config')
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
        print(f"Utworzono katalog konfiguracyjny: {config_dir}")
    return config_dir

def get_default_config():
    """Zwraca domyślną konfigurację"""
    return {
        "static_cells": {
            "month": "B2",
            "total_sales": "C6",
            "grot": "C7"
        },
        "categories_mapping": {},
        "last_update": datetime.now().isoformat()
    }

def update_categories_mapping():
    """Aktualizuje mapowanie kategorii w pliku konfiguracyjnym"""
    try:
        # Upewnij się, że katalog config istnieje
        config_dir = ensure_config_directory()
        config_path = os.path.join(config_dir, 'sheet_config.json')
        
        # Wczytaj istniejącą konfigurację lub utwórz nową
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print("Wczytano istniejącą konfigurację.")
        except (FileNotFoundError, json.JSONDecodeError):
            config = get_default_config()
            print("Utworzono nową konfigurację.")

        # Pobierz aktualne kategorie z Fakturowni
        print("Pobieranie kategorii z Fakturowni...")
        current_categories = get_categories()
        
        if not current_categories:
            print("Ostrzeżenie: Nie udało się pobrać kategorii z Fakturowni!")
            return config

        # Zachowaj istniejące mapowania
        existing_mappings = config.get("categories_mapping", {})
        
        # Stwórz nowe mapowanie
        new_mapping = {}
        
        # Dodaj wszystkie aktualne kategorie
        for cat_id, cat_name in current_categories.items():
            # Jeśli kategoria już istnieje, zachowaj jej konfigurację
            if cat_name in existing_mappings:
                new_mapping[cat_name] = existing_mappings[cat_name]
            else:
                # Dodaj nową kategorię bez przypisanej komórki
                new_mapping[cat_name] = {
                    "cell": "",
                    "fakturownia_id": cat_id
                }
                print(f"Dodano nową kategorię: {cat_name}")
        
        # Aktualizuj konfigurację
        config["categories_mapping"] = new_mapping
        config["last_update"] = datetime.now().isoformat()
        
        # Zapisz zaktualizowaną konfigurację
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"Zaktualizowano mapowanie kategorii. Znaleziono {len(current_categories)} kategorii.")
        print(f"Konfiguracja zapisana w: {config_path}")
        return config

    except Exception as e:
        print(f"Wystąpił błąd podczas aktualizacji mapowania kategorii: {e}")
        return None

if __name__ == "__main__":
    # Test funkcji przy bezpośrednim uruchomieniu pliku
    update_categories_mapping()
