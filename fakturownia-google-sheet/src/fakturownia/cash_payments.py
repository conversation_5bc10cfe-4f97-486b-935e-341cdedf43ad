import requests
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from dotenv import load_dotenv
import os
import json
from datetime import date, timedelta, datetime

# Określenie ścieżki do pliku .env
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env_path = os.path.join(current_dir, 'config', '.env')
load_dotenv(env_path)

FAKTUROWNIA_API_KEY = os.getenv("FAKTUROWNIA_API_KEY")
GOOGLE_SHEET_NAME = os.getenv("GOOGLE_SHEET_NAME")
YOUR_DOMAIN = "szczypta-natury.fakturownia.pl"


api_key = os.getenv('FAKTUROWNIA_API_KEY')
subdomain = "szczypta-natury"  # <-- z<PERSON><PERSON> jeśli inna subdomena

def get_cash_payments():
    """Pobiera dane o płatnościach gotówkowych z poprzedniego miesiąca"""
    print(f"Pobieranie faktur gotówkowych z poprzedniego miesiąca (data wystawienia)...")
    
    url = f"https://{subdomain}.fakturownia.pl/invoices.json"
    params = {
        'api_token': api_key,
        'per_page': 100,
        'page': 1,
        'period': 'last_month',
        'search_date_type': 'issue_date',
        'order': 'issue_date.desc'
    }
    
    all_cash_invoices_details = []
    total_cash = 0
    
    while True:
        response = requests.get(url, params=params)
        if response.status_code != 200:
            print(f"Błąd API: {response.status_code}, {response.text}")
            break

        invoices = response.json()
        if not invoices:
            break

        for invoice in invoices:
            if invoice.get('payment_type') == 'cash':
                # Bezpośrednio używamy price_gross
                price_gross = float(invoice.get('price_gross', '0').replace(',', '.'))
                total_cash += price_gross
                
                all_cash_invoices_details.append({
                    'number': invoice.get('number'),
                    'price_gross': price_gross,
                    'buyer_name': invoice.get('buyer_name'),
                    'issue_date': invoice.get('issue_date')
                })
                print(f"Dodano fakturę: {invoice.get('number')} na kwotę brutto: {price_gross} zł")

        params['page'] += 1

    # Wyświetlenie podsumowania
    print("\nFaktury gotówkowe z poprzedniego miesiąca:")
    for invoice in all_cash_invoices_details:
        print(f"Nr: {invoice['number']} | Brutto: {invoice['price_gross']} zł | Klient: {invoice['buyer_name']}")

    if not all_cash_invoices_details:
        print("Brak faktur gotówkowych w poprzednim miesiącu.")

    return {
        'total': total_cash,
        'invoices': all_cash_invoices_details
    }

if __name__ == "__main__":
    get_cash_payments()

result = {
    'date_from': '2024-02-01',
    'date_to': '2024-02-29',
    'total': 1234.56,
    'invoices': [
        {
            'number': 'FV/123',
            'price_net': 123.45,
            'buyer_name': 'Jan Kowalski',
            'issue_date': '2024-02-15'
        },
        # ...
    ]
}