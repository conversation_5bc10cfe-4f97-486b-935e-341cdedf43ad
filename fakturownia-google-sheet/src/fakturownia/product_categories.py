import requests
import os
from dotenv import load_dotenv
import json
from datetime import datetime

# Ładowanie zmiennych środowiskowych
load_dotenv()
api_key = os.getenv('FAKTUROWNIA_API_KEY')
subdomain = "szczypta-natury"

# Ścieżka do pliku cache
CACHE_FILE = 'products_cache.json'

def load_cache():
    """Ładuje cache z pliku"""
    try:
        if os.path.exists(CACHE_FILE):
            with open(CACHE_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"Błąd podczas ładowania cache: {str(e)}")
    return {}

def save_cache(cache):
    """Zapisuje cache do pliku"""
    try:
        with open(CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump(cache, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Błąd podczas zapisywania cache: {str(e)}")

def fetch_categories():
    """Pobiera listę wszystkich kategorii z Fakturowni"""
    url = f"https://{subdomain}.fakturownia.pl/categories.json"
    params = {'api_token': api_key}
    categories_map = {}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            categories = response.json()
            for category in categories:
                category_id = category.get('id')
                category_name = category.get('name')
                categories_map[category_id] = category_name
            print(f"Pobrano {len(categories_map)} kategorii")
            return categories_map
        else:
            print(f"Błąd przy pobieraniu kategorii: {response.status_code}")
            return {}
    except Exception as e:
        print(f"Wystąpił błąd podczas pobierania kategorii: {str(e)}")
        return {}

def fetch_all_products():
    """Pobiera wszystkie produkty z API"""
    url = f"https://{subdomain}.fakturownia.pl/products.json"
    params = {
        'api_token': api_key,
        'page': 1,
        'per_page': 100  # dodajemy paginację
    }
    products_data = {}
    
    while True:
        try:
            print(f"Pobieranie strony {params['page']} produktów...")
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                products = response.json()
                if not products:  # jeśli pusta strona, kończymy
                    break
                    
                for product in products:
                    product_id = str(product.get('id'))
                    category_id = product.get('category_id')
                    products_data[product_id] = {
                        'category_id': category_id,
                        'name': product.get('name')
                    }
                    print(f"Produkt: {product.get('name')} (ID: {product_id}) -> Kategoria ID: {category_id}")
                
                params['page'] += 1
            else:
                print(f"Błąd przy pobieraniu produktów: {response.status_code}")
                break
                
        except Exception as e:
            print(f"Wystąpił błąd podczas pobierania produktów: {str(e)}")
            break
    
    print(f"Łącznie pobrano dane {len(products_data)} produktów")
    return products_data

def get_product_data(product_id):
    """Pobiera dane produktu z cache lub API"""
    if not product_id:
        return None
        
    product_id = str(product_id)
    products_cache = load_cache()
    
    # Jeśli cache jest pusty, pobierz wszystkie produkty
    if not products_cache:
        products_cache = fetch_all_products()
        save_cache(products_cache)
    
    return products_cache.get(product_id)

def clear_cache():
    """Czyści cache"""
    if os.path.exists(CACHE_FILE):
        os.remove(CACHE_FILE)
        print("Cache wyczyszczony")

# Eksportujemy wszystkie potrzebne funkcje
__all__ = ['get_product_data', 'clear_cache', 'fetch_categories']

def test_product_category():
    """
    Funkcja testowa - sprawdza działanie cache'owania
    """
    test_id = 6121988  # przykładowe ID produktu
    
    print("Pierwsze pobranie (z API):")
    category1 = get_product_data(test_id)
    print(f"Produkt {test_id} należy do kategorii: {category1}")
    
    print("\nDrugie pobranie (z cache'u):")
    category2 = get_product_data(test_id)
    print(f"Produkt {test_id} należy do kategorii: {category2}")

if __name__ == "__main__":
    test_product_category() 