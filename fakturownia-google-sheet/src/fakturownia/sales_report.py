from playwright.sync_api import sync_playwright
import os
from dotenv import load_dotenv
from datetime import datetime, date
import calendar
import requests
import json
from ..utils.date_utils import get_previous_month_dates

# Określenie ścieżki do pliku .env
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env_path = os.path.join(current_dir, 'config', '.env')
load_dotenv(env_path)

login = os.getenv('FAKTUROWNIA_LOGIN')
password = os.getenv('FAKTUROWNIA_PASSWORD')
api_key = os.getenv('FAKTUROWNIA_API_KEY')
subdomain = "szczypta-natury"

def get_categories():
    """Pobiera listę wszystkich kategorii z API Fakturowni"""
    url = f"https://{subdomain}.fakturownia.pl/categories.json"
    params = {'api_token': api_key}
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # sprawdzi czy nie ma błędu HTTP
        categories = response.json()
        if not categories:
            print(f"Odpowiedź API: {response.text}")
            raise Exception("Nie otrzymano kategorii z API")
        return {cat['id']: cat['name'] for cat in categories}
    except Exception as e:
        print(f"Błąd podczas pobierania kategorii: {e}")
        print(f"Sprawdź czy API_KEY jest poprawny: {api_key}")
    return {}

def get_report_url(date_from, date_to, category_id=None):
    """Generuje URL raportu na podstawie parametrów"""
    base_url = f"https://{subdomain}.fakturownia.pl/reports/charts"
    params = {
        'pdf': 'no',
        'xls': 'false',
        'kind': 'revenue_and_expenses',
        'date_from': date_from,
        'date_to': date_to,
        'group_by': 'month',
        'query_date_kind': 'transaction_date',
        'more': 'yes',
        'price_kind': 'net',
        'document_kind': 'accounting_only',
        'currency_convert_to_main': 'true',
        'receipt_kind': 'receipts'
    }
    
    if category_id:
        params['category_ids[]'] = category_id
    
    query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
    return f"{base_url}?{query_string}"

def get_value_from_table(page):
    """Pobiera wartość z tabeli i formatuje ją odpowiednio"""
    try:
        # Znajdujemy ostatni wiersz tabeli (wiersz z sumą)
        selector = "table.table tbody tr:last-child th:last-child"
        value_element = page.query_selector(selector)
        
        if not value_element:
            print("Nie znaleziono elementu z wartością")
            return 0
            
        # Pobieramy tekst i czyścimy go
        value_text = value_element.inner_text()
        # Usuwamy 'zł', spacje i zamieniamy przecinek na kropkę
        clean_value = value_text.replace('zł', '').replace(' ', '').replace(',', '.').strip()
        
        return float(clean_value)
    except Exception as e:
        print(f"Błąd przy pobieraniu wartości: {e}")
        return 0

def get_grot_value(page, date_from, date_to):
    """Pobiera wartość dla faktur grot"""
    url = f"https://{subdomain}.fakturownia.pl/invoices"
    params = {
        'attachments': '',
        'buyer_company': '',
        'category_include_products': '1',
        'currency': '',
        'exact_search': '1',
        'form_action': '',
        'income': '',
        'job_id': '********',
        'kinds[]': 'accounting_only',
        'max_price_net': '',
        'min_price_net': '',
        'order': '',
        'payment_type': '',
        'period': 'last_month',
        'procedure_designations': '',
        'query': 'grot',
        'search_date_type': 'issue_date',
        'send_pdf': '',
        'sending_overdues': '',
        'status': '',
        'user_id': ''
    }
    query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
    full_url = f"{url}?{query_string}"
    
    print("\nPobieranie wartości GROT...")
    page.goto(full_url)
    page.wait_for_selector('#total_price_net_values')
    
    value_element = page.locator('#total_price_net_values')
    value_text = value_element.inner_text()
    # Czyścimy tekst z "PLN" i formatujemy
    clean_value = value_text.replace('PLN', '').replace(' ', '').replace(',', '.').strip()
    value = float(clean_value)
    print(f"Wartość GROT: {value:.2f} zł")
    return value

def scrape_report_data():
    # Pobieranie dat z poprzedniego miesiąca
    date_from, date_to = get_previous_month_dates()
    
    # Formatujemy daty
    date_from_str = date_from.strftime('%Y-%m-%d')
    date_to_str = date_to.strftime('%Y-%m-%d')
    
    print(f"Pobieranie danych za okres: {date_from_str} - {date_to_str}")
    
    categories = get_categories()
    if not categories:
        print("Nie można kontynuować bez kategorii!")
        return
        
    print(f"Pobrano {len(categories)} kategorii")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        
        try:
            print("Logowanie do Fakturowni...")
            page.goto('https://szczypta-natury.fakturownia.pl/login')
            page.wait_for_load_state('networkidle')
            
            # Obsługa okna z ciasteczkami
            try:
                # Nowy selektor dla przycisku akceptacji ciasteczek
                cookie_button = page.locator('a.cmpboxbtn.cmpboxbtnyes')
                if cookie_button.is_visible():
                    cookie_button.click()
                    page.wait_for_load_state('networkidle')
            except Exception as e:
                print(f"Nie udało się obsłużyć okna ciasteczek: {e}")
            
            # Teraz możemy się zalogować
            page.fill('#user_session_login', login)
            page.fill('#user_session_password', password)
            page.click('input[value="Zaloguj"]')
            
            page.wait_for_load_state('networkidle')
            
            results = {}
            
            # Pobierz wartość GROT
            results['grot'] = {
                'id': 'grot',
                'value': get_grot_value(page, date_from_str, date_to_str)
            }
            
            # Pobierz sumę całkowitą
            total_url = get_report_url(date_from_str, date_to_str)
            print("\nPobieranie sumy całkowitej...")
            page.goto(total_url)
            page.wait_for_selector('table.table')
            results['total'] = get_value_from_table(page)
            print(f"Suma całkowita: {results['total']:.2f} zł")
            
            # Następnie pobierz dane dla każdej kategorii
            print("\nPobieranie danych per kategoria...")
            for cat_id, cat_name in categories.items():
                print(f"\nAnaliza kategorii: {cat_name}")
                url = get_report_url(date_from_str, date_to_str, cat_id)
                page.goto(url)
                
                try:
                    page.wait_for_selector('table.table')
                    value = get_value_from_table(page)
                    results[cat_name] = {
                        'id': cat_id,
                        'value': value
                    }
                    print(f"{cat_name}: {value:.2f} zł")
                except Exception as e:
                    print(f"Błąd przy pobieraniu danych dla kategorii {cat_name}: {e}")
                    results[cat_name] = {
                        'id': cat_id,
                        'value': 0
                    }
            
            # Zapisz wyniki do pliku
            with open('raport_sprzedazy.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            # Wyświetl podsumowanie
            print("\nPODSUMOWANIE SPRZEDAŻY")
            print("=" * 50)
            for cat_name, data in results.items():
                if cat_name != 'total':
                    print(f"{cat_name}: {data['value']:.2f} zł")
            print("=" * 50)
            print(f"SUMA CAŁKOWITA: {results['total']:.2f} zł")
            
            # Przed zwróceniem wyników dodajemy daty
            results = {
                'date_from': date_from_str,
                'date_to': date_to_str,
                'total': results['total'],
                'grot': results['grot'],
                'categories': {cat_name: data for cat_name, data in results.items() 
                              if cat_name not in ['total', 'grot', 'date_from', 'date_to']}
            }
            
            return results
            
        except Exception as e:
            print(f"Wystąpił błąd: {str(e)}")
            return None
        finally:
            browser.close()

if __name__ == "__main__":
    scrape_report_data()
