from datetime import date
import calendar
from typing import <PERSON><PERSON>

def get_previous_month_dates() -> <PERSON><PERSON>[date, date]:
    """
    Oblicza daty początku i końca poprzedniego miesiąca.
    
    Returns:
        <PERSON>ple[date, date]: Krotka zawierająca datę początku i końca poprzedniego miesiąca
    """
    today = date.today()
    if today.month == 1:
        previous_month = 12
        year = today.year - 1
    else:
        previous_month = today.month - 1
        year = today.year
    
    date_from = date(year, previous_month, 1)
    last_day = calendar.monthrange(year, previous_month)[1]
    date_to = date(year, previous_month, last_day)
    
    return date_from, date_to 