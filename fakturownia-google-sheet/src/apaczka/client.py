import os
import httpx
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, date
import calendar
from dotenv import load_dotenv
from ..utils.date_utils import get_previous_month_dates
import hmac
import hashlib

# Ładowanie zmiennych środowiskowych
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env_path = os.path.join(current_dir, 'config', '.env')
load_dotenv(env_path)

class ApaczkaClient:
    def __init__(self):
        print("Próba wczytania zmiennych środowiskowych...")
        self.app_id = os.getenv('APACZKA_APP_ID')
        self.app_secret = os.getenv('APACZKA_APP_SECRET')
        
        print(f"Wczytane wartości:")
        print(f"APACZKA_APP_ID: {self.app_id}")
        print(f"APACZKA_APP_SECRET: {self.app_secret}")
        print(f"Ścieżka do .env: {env_path}")
        
        if not self.app_id or not self.app_secret:
            raise ValueError(f"Brak wymaganych danych autoryzacyjnych Apaczki w pliku .env. "
                           f"APP_ID: {'znaleziono' if self.app_id else 'brak'}, "
                           f"APP_SECRET: {'znaleziono' if self.app_secret else 'brak'}")
        self.base_url = "https://www.apaczka.pl/api/v2"
        self.headers = {
            "Authorization": f"Bearer {self.app_secret}",
            "Content-Type": "application/json",
            # TODO: Dodać nagłówek z sygnaturą - potrzebujemy informacji z dokumentacji
            # jak generować tę sygnaturę
        }

    def _get_previous_month_dates(self) -> Tuple[date, date]:
        """
        Oblicza daty początku i końca poprzedniego miesiąca.
        
        Returns:
            Tuple[date, date]: Krotka zawierająca datę początku i końca poprzedniego miesiąca
        """
        today = date.today()
        if today.month == 1:
            previous_month = 12
            year = today.year - 1
        else:
            previous_month = today.month - 1
            year = today.year
        
        date_from = date(year, previous_month, 1)
        last_day = calendar.monthrange(year, previous_month)[1]
        date_to = date(year, previous_month, last_day)
        
        return date_from, date_to

    async def get_shipments(self, page: int = 1, limit: int = 25) -> List[Dict]:
        """
        Pobiera listę zamówień z API Apaczki.
        """
        if limit > 25:
            limit = 25
        
        expires = int(datetime.now().timestamp()) + 300
        
        request_data = {
            'page': page,
            'limit': limit
        }
        
        data = {
            'app_id': self.app_id,
            'request': json.dumps(request_data),
            'expires': expires,
            'signature': self._generate_signature('orders', request_data, expires)
        }
        
        print("\nWysyłane dane:")
        print(json.dumps(data, indent=2))
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/orders/",
                data=data
            )
            
            print("\nOtrzymana odpowiedź:")
            print(f"Status: {response.status_code}")
            print(f"Treść: {response.text}")
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"Błąd API Apaczki: {response.text}")

    async def get_shipment_status(self, shipment_id: str) -> Dict[str, Any]:
        """
        Pobiera status konkretnej przesyłki.
        
        Args:
            shipment_id: ID przesyłki
            
        Returns:
            Dict zawierający status przesyłki
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/shipments/{shipment_id}/status",
                headers=self.headers
            )
            
            if response.status_code != 200:
                raise Exception(f"Błąd podczas pobierania statusu przesyłki: {response.text}")
                
            return response.json()

    def format_shipment_data(self, orders: List[Dict]) -> Dict[str, Any]:
        total_cost = 0
        
        return {
            "total_shipments": len(orders),
            "total_cost": total_cost,
            "shipments": [
                {
                    "number": order.get("waybill_number"),
                    "status": order.get("status"),
                    "created": order.get("created"),
                    "delivered": order.get("delivered")
                }
                for order in orders
            ]
        }

    def _count_shipments_by_status(self, shipments: List[Dict]) -> Dict[str, int]:
        """
        Liczy przesyłki według statusu.
        
        Args:
            shipments: Lista przesyłek
            
        Returns:
            Dict z liczbą przesyłek dla każdego statusu
        """
        if not isinstance(shipments, list):
            return {}
        
        status_counts = {}
        for shipment in shipments:
            if isinstance(shipment, dict):
                status = shipment.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
        return status_counts

    def _generate_signature(self, route: str, request_data: Dict, expires: int) -> str:
        """
        Generuje sygnaturę HMAC-SHA256 wymaganą przez API.
        """
        string_to_sign = f"{self.app_id}:orders:{json.dumps(request_data)}:{expires}"
        
        signature = hmac.new(
            self.app_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature 