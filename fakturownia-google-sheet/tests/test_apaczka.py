import asyncio
import sys
import os
from datetime import date
from dotenv import load_dotenv

# Do<PERSON>ie ścieżki do src do PYTHONPATH
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Ładowanie zmiennych środowiskowych
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
env_path = os.path.join(current_dir, 'config', '.env')
load_dotenv(env_path)

from src.apaczka.client import ApaczkaClient

async def test_apaczka_connection():
    try:
        print("Inicjalizacja klienta Apaczki...")
        client = ApaczkaClient()
        
        print("\nTest 1: Pobieranie listy przesyłek...")
        response = await client.get_shipments()
        print(f"Liczba pobranych przesyłek: {len(response)}")
        
        if response:
            print("\nPrzykładowa przesyłka:")
            print(response[0])
            
            # Test formatowania danych
            print("\nTest 2: Formatowanie danych...")
            formatted_data = client.format_shipment_data(response)
            print("\nSformatowane dane:")
            print(f"Liczba przesyłek: {formatted_data['total_shipments']}")
            print(f"Suma kosztów: {formatted_data['total_cost']:.2f} zł")
            
            # Test liczenia przesyłek według statusu
            print("\nTest 3: Liczenie przesyłek według statusu...")
            status_counts = client._count_shipments_by_status(response)
            print("\nLiczba przesyłek w każdym statusie:")
            for status, count in status_counts.items():
                print(f"{status}: {count}")
        
    except Exception as e:
        print(f"\nWystąpił błąd: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("Rozpoczynam testy modułu Apaczki...")
    success = asyncio.run(test_apaczka_connection())
    
    if success:
        print("\nTesty zakończone sukcesem!")
    else:
        print("\nTesty zakończone błędem!") 