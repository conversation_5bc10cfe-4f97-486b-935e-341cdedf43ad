import os
import sys
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Ładowanie zmiennych środowiskowych PRZED importami
load_dotenv('config/.env')

# Dodanie ścieżki do src względem lokalizacji main.py
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.fakturownia.sales_report import scrape_report_data
from src.fakturownia.cash_payments import get_cash_payments
from src.fakturownia.category_mapper import update_categories_mapping
from src.google_sheets.sheet_updater import SheetUpdater

def main():
    try:
        print("Start procesu aktualizacji danych...")
        
        # 1. Aktualizacja mapowania kategorii
        print("\nAktualizacja mapowania kategorii...")
        if not update_categories_mapping():
            raise Exception("Nie udało się zaktualizować mapowania kategorii")
        
        # 2. Pobieranie danych z Fakturowni
        print("\nPobieranie danych z Fakturowni...")
        sales_data = scrape_report_data()
        cash_data = get_cash_payments()
        
        if not sales_data:
            raise Exception("Nie udało się pobrać danych sprzedażowych")
            
        # 3. Inicjalizacja połączenia z Google Sheets
        sheet_updater = SheetUpdater()
        
        # 4. Czyszczenie arkusza
        print("\nCzyszczenie arkusza Google...")
        if not sheet_updater.clear_sheet():
            raise Exception("Nie udało się wyczyścić arkusza")
        
        # 5. Wklejanie nowych danych
        print("\nAktualizacja arkusza nowymi danymi...")
        if not sheet_updater.update_monthly_report(sales_data, cash_data):
            raise Exception("Nie udało się zaktualizować arkusza")
            
        print("\nProces zakończony sukcesem!")
        
    except Exception as e:
        print(f"\nWystąpił błąd: {e}")
        return False
        
    return True

if __name__ == "__main__":
    main()
