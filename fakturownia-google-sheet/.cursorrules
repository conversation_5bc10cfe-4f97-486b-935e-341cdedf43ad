You are an expert in Python, FastAPI, and scalable API development. Your primary task is to build a service on a VPS server that retrieves data from the Fakturowni API and transfers it—appropriately structured—into a specified Google Sheet. Below are the guidelines you should follow to accomplish this effectively.

Key Principles
Write concise, technical responses with accurate Python examples that demonstrate best practices for interacting with external APIs (Fakturowni) and Google Sheets.
Use functional, declarative programming; avoid classes where possible to keep the codebase straightforward and maintainable.
Prefer iteration and modularization over code duplication, especially when working with data transformation from Fakturowni and sending data to Google Sheets.
Use descriptive variable names with auxiliary verbs (e.g., is_active, has_permission) to increase readability.
Use lowercase with underscores for directories and files (e.g., routers/fakturowni_routes.py, utils/google_sheets.py).
Favor named exports for routes and utility functions to maintain clarity and consistency.
Use the Receive an Object, Return an Object (RORO) pattern when designing your data transformation functions.
Python/FastAPI
Function Signatures
Use def for pure functions that do not require asynchronous operations.
Use async def for all I/O operations, including database queries, HTTP requests to Fakturowni, and calls to the Google Sheets API.
Type Hints
Include type hints in all function signatures.
Prefer using Pydantic models over raw dictionaries for validating incoming Fakturowni data and outgoing responses.
File Structure
Keep separate modules for router definitions, sub-routes, utilities (e.g., for Fakturowni and Google Sheets), static content, and data models/schemas.
For example:
markdown
Kopiuj
Edytuj
- routers
  - fakturowni_routes.py
  - google_sheets_routes.py
- utils
  - fakturowni_client.py
  - google_sheets_client.py
- schemas
  - fakturowni_models.py
  - google_sheets_models.py
- main.py
Conditional Statements
Avoid unnecessary curly braces.
Use concise, one-line syntax for simple conditionals (e.g., if condition: do_something()).
Error Handling and Validation
Handle Errors Early
Validate credentials and request parameters at the beginning of functions.
For example, if Fakturowni credentials or Google Sheets API keys are missing, return an error immediately.
Use Early Returns
If data from Fakturowni is not in the expected format, return an error before continuing with further transformations.
Place the happy path last in the function for clarity.
Avoid Unnecessary Else Statements
Use guard clauses to handle invalid states early.
Consistency
Use custom error types or error factories for consistent error handling across the application (e.g., FakturowniError, GoogleSheetsError).
User-Friendly Messages
Provide sufficient context in error messages, e.g., “Invalid Fakturowni response: missing invoice data.”
Dependencies
FastAPI
For building scalable, high-performance APIs.
Pydantic v2
For advanced data validation and parsing, particularly for Fakturowni and Google Sheets data structures.
Async Database Libraries
If you need to store logs or intermediate data locally, use asynchronous libraries such as asyncpg or aiomysql.
SQLAlchemy 2.0 (Optional)
If you opt for an ORM approach for storing data locally before or after processing.
External API Clients
Use httpx or aiohttp for making asynchronous requests to the Fakturowni API.
Google Sheets API
Integrate the official Google Sheets API client (e.g., google-api-python-client, or a specialized library that supports asynchronous patterns, if available).
Authentication
Ensure secure storage and loading of Fakturowni and Google credentials (e.g., environment variables or a secured configuration file).
FastAPI-Specific Guidelines
Pydantic Models
Use functional components and Pydantic models for input validation and response schemas.
For example, define a FakturowniInvoice model in schemas/fakturowni_models.py that validates the structure of the invoice data.
Declarative Route Definitions
Use clear return type annotations and descriptive endpoint names (e.g., /fakturowni/invoices, /sheets/update-invoices).
Synchronous vs Asynchronous
Use async def for routes that call the Fakturowni API or Google Sheets API.
If a route is purely computational, def may be sufficient.
Lifecycle Events
Minimize global @app.on_event("startup") and @app.on_event("shutdown").
Prefer lifespan context managers for resource initialization and teardown (e.g., setting up Google Sheets service).
Middleware
Implement middleware for logging, error monitoring, and performance analytics.
HTTPException Usage
For expected errors (e.g., missing required invoice IDs or network failures), raise a HTTPException with the appropriate status code and human-readable message.
Unexpected Errors
Use custom error handling or middleware for capturing unexpected exceptions.
Performance Optimization
Non-Blocking I/O
Use asynchronous requests for Fakturowni data retrieval and Google Sheets updates.
Batch requests where possible to reduce round-trips (e.g., updating multiple rows in Google Sheets at once).
Caching
For static or frequently accessed data, implement caching (e.g., Redis or in-memory) to avoid redundant calls to Fakturowni.
Data Serialization
Leverage Pydantic’s efficient data parsing and serialization, especially when dealing with large invoice sets from Fakturowni.
Lazy Loading
When possible, load only the necessary invoice details instead of fetching all invoice data at once.
Scalability
Structure your application so that it can easily be deployed or scaled on the VPS (e.g., using Docker containers or process managers like uvicorn/gunicorn).
Key Conventions
FastAPI Dependency Injection
Rely on FastAPI’s dependency injection for managing state and shared resources, such as authenticated Fakturowni clients or authenticated Google Sheets clients.
API Performance Metrics
Monitor response time, latency, and throughput.
Ensure that calls to Fakturowni and Google Sheets are optimized to handle network or authentication delays gracefully.
Limit Blocking Operations in Routes
Favor asynchronous flows for all I/O-bound tasks.
Use dedicated async functions for:
Fetching invoices from Fakturowni.
Updating Google Sheets.
Clearly separate route definitions, utility functions, and data models for better readability and maintainability.
Refer to the official FastAPI documentation for best practices in Data Models, Path Operations, Middleware, and advanced usage. Apply the same rigor when working with the Fakturowni and Google Sheets APIs to ensure consistent, secure, and reliable data management on your VPS server.