#!/bin/bash
# Skrypt aktualizacji n8n do wersji 1.103.2
# Utworzony: 2025-07-25T08:30:54Z

set -e

echo "=== AKTUALIZACJA N8N DO WERSJI 1.103.2 ==="
echo "Data: $(date)"
echo ""

cd /home/<USER>/projekty/n8n

echo "1. Zatrzymywanie kontenerów..."
docker-compose down

echo "2. Backup obecnej konfiguracji..."
cp docker-compose.yml docker-compose.yml.backup.$(date +%s)

echo "3. Zastąpienie konfiguracji nową wersją..."
cp docker-compose.yml.new docker-compose.yml

echo "4. Pobieranie nowych obrazów..."
docker-compose pull

echo "5. Uruchamianie z nową wersją..."
docker-compose up -d

echo "6. Oczekiwanie na uruchomienie..."
sleep 30

echo "7. Sprawdzanie statusu..."
docker-compose ps

echo ""
echo "=== AKTUALIZACJA ZAKOŃCZONA ==="
echo "Sprawdź czy n8n działa: http://127.0.0.1:5678"
