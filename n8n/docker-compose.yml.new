version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: n8n_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n_db
      POSTGRES_USER: n8n_user
      POSTGRES_PASSWORD: super_tajne_haslo
    volumes:
      - n8n_postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 512M
    ports:
      - "127.0.0.1:5433:5432"  # Bind tylko do localhost
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_user -d n8n_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  n8n:
    image: n8nio/n8n:1.103.2
    container_name: n8n
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - N8N_HOST=n8n.simetria.pl
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - N8N_SSL_PORT=443
      - WEBHOOK_URL=https://n8n.simetria.pl
      - N8N_ALLOW_EXTERNAL_ACCESS=true
      - N8N_SECURE_COOKIE=true
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_RUNNERS_ENABLED=true
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_db
      - DB_POSTGRESDB_USER=n8n_user
      - DB_POSTGRESDB_PASSWORD=super_tajne_haslo
      - TZ=Europe/Warsaw
      # === RETENCJA WYKONAŃ - KONTROLA ROZMIARU BAZY ===
      - N8N_EXECUTIONS_DATA_PRUNE=true
      - N8N_EXECUTIONS_DATA_MAX_AGE=336  # 14 dni w godzinach (14 * 24)
      - N8N_EXECUTIONS_DATA_PRUNE_MAX_COUNT=3000  # maksymalnie 3000 wykonań
      - N8N_EXECUTIONS_DATA_PRUNE_HARD_DELETE_INTERVAL=1800  # czyść co 30 min
      - N8N_LOG_LEVEL=info  # zmniejsz verbose logging
      # === NOWE USTAWIENIA DLA WERSJI 1.103+ ===
      - N8N_METRICS=true  # Włącz metryki
      - N8N_DIAGNOSTICS_ENABLED=false  # Wyłącz diagnostykę
    ports:
      - "127.0.0.1:5678:5678"  # Bind tylko do localhost
    volumes:
      - n8n_data:/home/<USER>/.n8n
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  n8n_postgres_data:
  n8n_data:
