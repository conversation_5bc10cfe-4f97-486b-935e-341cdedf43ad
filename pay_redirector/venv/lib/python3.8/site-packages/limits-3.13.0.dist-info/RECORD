limits-3.13.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
limits-3.13.0.dist-info/LICENSE.txt,sha256=T6i7kq7F5gIPfcno9FCxU5Hcwm22Bjq0uHZV3ElcjsQ,1061
limits-3.13.0.dist-info/METADATA,sha256=vQsvBw6YR-jK6cCIusRrIjc-UG-aSL4PoHqFfYKzvjE,7170
limits-3.13.0.dist-info/RECORD,,
limits-3.13.0.dist-info/WHEEL,sha256=cpQTJ5IWu9CdaPViMhC9YzF8gZuS5-vlfoFihTBC86A,91
limits-3.13.0.dist-info/top_level.txt,sha256=C7g5ahldPoU2s6iWTaJayUrbGmPK1d6e9t5Nn0vQ2jM,7
limits/__init__.py,sha256=j_yVhgN9pdz8o5rQjVwdJTBSq8F-CTzof9kkiYgjRbw,728
limits/__pycache__/__init__.cpython-38.pyc,,
limits/__pycache__/_version.cpython-38.pyc,,
limits/__pycache__/errors.cpython-38.pyc,,
limits/__pycache__/limits.cpython-38.pyc,,
limits/__pycache__/strategies.cpython-38.pyc,,
limits/__pycache__/typing.cpython-38.pyc,,
limits/__pycache__/util.cpython-38.pyc,,
limits/__pycache__/version.cpython-38.pyc,,
limits/_version.py,sha256=WegVz4YKhtYN102E8QvX81l0oVnN2UGchIOm2eVCePg,498
limits/aio/__init__.py,sha256=IOetunwQy1c5GefzitK8lewbTzHGiE-kmE9NlqSdr3U,82
limits/aio/__pycache__/__init__.cpython-38.pyc,,
limits/aio/__pycache__/strategies.cpython-38.pyc,,
limits/aio/storage/__init__.py,sha256=CbtuSlVl1jPyN_vsEI_ApWblDblVaL46xcZ2M_oM0V8,595
limits/aio/storage/__pycache__/__init__.cpython-38.pyc,,
limits/aio/storage/__pycache__/base.cpython-38.pyc,,
limits/aio/storage/__pycache__/etcd.cpython-38.pyc,,
limits/aio/storage/__pycache__/memcached.cpython-38.pyc,,
limits/aio/storage/__pycache__/memory.cpython-38.pyc,,
limits/aio/storage/__pycache__/mongodb.cpython-38.pyc,,
limits/aio/storage/__pycache__/redis.cpython-38.pyc,,
limits/aio/storage/base.py,sha256=xdYpBBonyMjxE9iT-2oZjm6x29aDU6Xd09MeBYbZcMo,4817
limits/aio/storage/etcd.py,sha256=Rjb_EYKFRr4F2Z6zvAPP9vQOyXJQHaju3VjxxUs75_c,4791
limits/aio/storage/memcached.py,sha256=6aTlACfCtchdcZqoisnei0MOlCH7yLV9A1yCjOE5f9g,4802
limits/aio/storage/memory.py,sha256=DlmWluqUwBUWQIQ6XZi-mPrb15vfzBhA4iAKhBELDnE,5856
limits/aio/storage/mongodb.py,sha256=sFQQK-JuInGEXC5Upjk_9NBe9hoNhgxQYimdRraXxQ8,9392
limits/aio/storage/redis.py,sha256=jkqtdIwTpfXTXwgTWTA1Jlc3Lpc-vnu4XRy6CIptiZA,15651
limits/aio/strategies.py,sha256=SHjmJnmy7Nh4tBydkA-0qPaULYcLOAM91T4RPybq0Sg,6768
limits/errors.py,sha256=xCKGOVJiD-g8FlsQQb17AW2pTUvalYSuizPpvEVoYJE,626
limits/limits.py,sha256=ZsXESq2e1ji7c2ZKjSkIAasCjiLdjVLPUa9oah_I8U4,4943
limits/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
limits/resources/redis/lua_scripts/acquire_moving_window.lua,sha256=5CFJX7D6T6RG5SFr6eVZ6zepmI1EkGWmKeVEO4QNrWo,483
limits/resources/redis/lua_scripts/clear_keys.lua,sha256=zU0cVfLGmapRQF9x9u0GclapM_IB2pJLszNzVQ1QRK4,184
limits/resources/redis/lua_scripts/incr_expire.lua,sha256=Uq9NcrrcDI-F87TDAJexoSJn2SDgeXIUEYozCp9S3oA,195
limits/resources/redis/lua_scripts/moving_window.lua,sha256=iAInenlVd_fFDi15APpRWbOuPUz_G3nFnVAqb7wOedA,398
limits/storage/__init__.py,sha256=XAW1jVDMLFkPr_Tl1SXpg_p4Y3nhEatTSYq1MlnYJcA,2564
limits/storage/__pycache__/__init__.cpython-38.pyc,,
limits/storage/__pycache__/base.cpython-38.pyc,,
limits/storage/__pycache__/etcd.cpython-38.pyc,,
limits/storage/__pycache__/memcached.cpython-38.pyc,,
limits/storage/__pycache__/memory.cpython-38.pyc,,
limits/storage/__pycache__/mongodb.cpython-38.pyc,,
limits/storage/__pycache__/redis.cpython-38.pyc,,
limits/storage/__pycache__/redis_cluster.cpython-38.pyc,,
limits/storage/__pycache__/redis_sentinel.cpython-38.pyc,,
limits/storage/__pycache__/registry.cpython-38.pyc,,
limits/storage/base.py,sha256=fDdYLa-RrnjhBTO1hE5aTTM8q8n3M5HD-65KyWWXBtg,4627
limits/storage/etcd.py,sha256=wkC_mj4Tsf2nwUKByMiHiGzA40N3mDepEwdLmvH8wmw,4484
limits/storage/memcached.py,sha256=bMzfZgYa_EWcZAjSZLcygpk3hpeOAErBpRE8dVwyXQs,6640
limits/storage/memory.py,sha256=R16E-Ccnmn1-LlolkFf-kB1-QHh8eiwFFLYVv0PuFD0,5561
limits/storage/mongodb.py,sha256=QWd_SW--P86SMPDrDrBQS-2xKbWY3cw9x71KsI506zY,9213
limits/storage/redis.py,sha256=3zJ1gDMDepT_pGN9d2aAN7Pea7tMBI49VK60IHv-Ooc,8452
limits/storage/redis_cluster.py,sha256=KwhWV0v3_TliRyS3OU15IlpeC8gRQr29U4FkcME01fo,5380
limits/storage/redis_sentinel.py,sha256=7PVB0hBl0I_enhN_h9QSJTE7zGuYtjkebotTqxm2iZo,3875
limits/storage/registry.py,sha256=xcBcxuu6srqmoS4WqDpkCXnRLB19ctH98v21P8S9kS8,708
limits/strategies.py,sha256=Zy6PIhkysPbxnMzFjyXEsxMM6jhRoQ5XT5WskTNruK0,6949
limits/typing.py,sha256=nwJLek44QIg3869AbOSvPwotfp6JR7vEHz_UgZBiqgg,3287
limits/util.py,sha256=xMRR5bKksYcnzY0H0xORDGvRFF5btiBognY2sSd38NE,5743
limits/version.py,sha256=YwkF3dtq1KGzvmL3iVGctA8NNtGlK_0arrzZkZGVjUs,47
