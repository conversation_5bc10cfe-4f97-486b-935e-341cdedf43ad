
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-06-22T18:39:54-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "7b87c4d37659ae5fe0a8bf7216bfff789facd5f3",
 "version": "3.13.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
