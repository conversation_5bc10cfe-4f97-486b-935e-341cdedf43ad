
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-07-20T18:51:17-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "812189d8bda77d54f537b9360c9bb32998eef22b",
 "version": "3.8.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
