require('dotenv').config();

const fastify = require('fastify')({ logger: true }); // Włączamy logger Fastify dla lepszego debugowania
const fastifyFormbody = require('@fastify/formbody');
const fastifyWebsocket = require('@fastify/websocket');
const Twilio = require('twilio');
const WebSocket = require('ws'); // Klient WebSocket do połączenia z ElevenLabs

// --- Konfiguracja odczytana z .env ---
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;
const ELEVENLABS_AGENT_ID = process.env.ELEVENLABS_AGENT_ID;
const ELEVENLABS_MODEL_ID = process.env.ELEVENLABS_MODEL_ID || 'eleven_multilingual_v2'; // Odczyt modelu z .env
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER;
const SERVER_PORT = process.env.SERVER_PORT || 3000;
const YOUR_SERVER_PUBLIC_URL = process.env.YOUR_SERVER_PUBLIC_URL;

// Sprawdzenie, czy wszystkie niezbędne zmienne środowiskowe są ustawione
if (!ELEVENLABS_API_KEY || !ELEVENLABS_AGENT_ID || !TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER || !YOUR_SERVER_PUBLIC_URL) {
    fastify.log.error('Krytyczny błąd: Brak niezbędnych zmiennych środowiskowych. Sprawdź plik .env');
    process.exit(1); // Zakończ proces, jeśli brakuje konfiguracji
}

// Inicjalizacja klienta Twilio
const twilioClient = new Twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

// Rejestracja pluginów Fastify
fastify.register(fastifyFormbody);
fastify.register(fastifyWebsocket);

// --- Endpointy HTTP ---

fastify.post('/initiate-call', async (request, reply) => {
    fastify.log.info(`Otrzymano żądanie /initiate-call z danymi: ${JSON.stringify(request.body)}`);
    const { phoneNumber, recipientName, callContext } = request.body; 

    if (!phoneNumber) {
        reply.code(400).send({ error: 'Brak parametru phoneNumber' });
        return;
    }

    try {
        fastify.log.info(`Inicjowanie połączenia do: ${phoneNumber}`);
        
        const twimlWebhookUrl = `${YOUR_SERVER_PUBLIC_URL}/twilio-voice-webhook`; // Usunięto parametry z URL, Twilio prześle je w ciele POST

        const call = await twilioClient.calls.create({
            to: phoneNumber,
            from: TWILIO_PHONE_NUMBER,
            url: twimlWebhookUrl,
            method: 'POST',
            // Przekazanie parametrów do TwiML aplikacji przez Twilio jako parametry żądania POST
            // Twilio automatycznie przekaże parametry takie jak CallSid, To, From.
            // Możemy dodać własne parametry używając `ApplicationParameters` jeśli `url` wskazuje na TwiML App,
            // ale dla prostego URL TwiML, Twilio nie przekaże niestandardowych parametrów w query string tak łatwo.
            // Lepszym podejściem jest przekazanie ich w `statusCallback` lub zapisanie ich tymczasowo po stronie serwera powiązanych z CallSid
            // Na razie, polegajmy na tym, że n8n może przekazać kontekst do endpointu WebSocket.
            // Lub zmodyfikujemy logikę, aby zapisać kontekst po stronie serwera przy inicjacji.
        });

        fastify.log.info(`Połączenie zainicjowane, SID: ${call.sid}`);
        // Zapiszmy kontekst rozmowy powiązany z call.sid, aby móc go użyć w WebSocket handlerze
        // To wymaga jakiegoś prostego storage (np. obiekt w pamięci dla celów demonstracyjnych)
        // W produkcji lepiej użyć np. Redis.
        if (!fastify.callContexts) {
            fastify.callContexts = {};
        }
        fastify.callContexts[call.sid] = { recipientName, callContext: callContext ? JSON.parse(callContext) : {} };


        reply.send({ success: true, message: 'Połączenie inicjowane', callSid: call.sid });

    } catch (error) {
        fastify.log.error(`Błąd podczas inicjowania połączenia: ${error.message}`);
        reply.code(500).send({ error: 'Błąd serwera podczas inicjowania połączenia', details: error.message });
    }
});

fastify.post('/twilio-voice-webhook', (request, reply) => {
    const { CallSid, From, To } = request.body; // Standardowe parametry od Twilio
    fastify.log.info(`Żądanie TwiML dla CallSid: ${CallSid}, Od: ${From}, Do: ${To}`);

    const twiml = new Twilio.twiml.VoiceResponse();
    const connect = twiml.connect();
    const webSocketUrlForTwilio = `${YOUR_SERVER_PUBLIC_URL.replace(/^http/, 'ws')}/ws/twilio-audio/${CallSid}`;
    
    connect.stream({
        url: webSocketUrlForTwilio,
    });
    
    reply.type('text/xml').send(twiml.toString());
});


fastify.get('/ws/twilio-audio/:callSid', { websocket: true }, (connection /* to jest obiekt WebSocket z Twilio */, req /* obiekt żądania HTTP */) => {
    const callSid = req.params.callSid;
    fastify.log.info(`Połączenie WebSocket od Twilio dla CallSid [${callSid}] zostało nawiązane!`);

    const contextData = fastify.callContexts && fastify.callContexts[callSid] ? fastify.callContexts[callSid] : {};
    const initialConversationContext = contextData.callContext || {};
    const recipientName = contextData.recipientName || '';
    fastify.log.info(`[${callSid}] Odzyskany kontekst: ${JSON.stringify(initialConversationContext)}, Imię odbiorcy: ${recipientName}`);


    let elevenLabsSocket;
    const elevenLabsUrl = `wss://api.elevenlabs.io/v1/agent-websockets/${ELEVENLABS_AGENT_ID}/connect`;

    try {
        elevenLabsSocket = new WebSocket(elevenLabsUrl, {
            headers: { 'xi-api-key': ELEVENLABS_API_KEY }
        });
    } catch (e) {
        fastify.log.error(`[${callSid}] Błąd tworzenia WebSocket do ElevenLabs: ${e.message}`);
        connection.socket.close(1011, "Błąd połączenia z agentem AI");
        return;
    }
    
    elevenLabsSocket.onopen = () => {
        fastify.log.info(`[${callSid}] Połączono z ElevenLabs WebSocket! Wysyłanie konfiguracji...`);
        if (elevenLabsSocket && elevenLabsSocket.readyState === WebSocket.OPEN) {
            elevenLabsSocket.send(JSON.stringify({
                "audio_format": "pcm_mulaw_8000",
                "model_id": ELEVENLABS_MODEL_ID,
                "conversation_context": initialConversationContext,
                "enable_updates": true 
            }));
        }
    };

    elevenLabsSocket.onmessage = (event) => {
        const dataFromElevenLabs = JSON.parse(event.data);
        fastify.log.debug(`[${callSid}] Otrzymano wiadomość od ElevenLabs: Klucze ${Object.keys(dataFromElevenLabs).join(', ')}`);

        if (dataFromElevenLabs.audio_base64) {
            if (connection.streamSid) {
                 connection.socket.send(JSON.stringify({
                    event: 'media',
                    streamSid: connection.streamSid,
                    media: {
                        payload: dataFromElevenLabs.audio_base64,
                    },
                }));
            } else {
                fastify.log.warn(`[${callSid}] Brak streamSid od Twilio, nie można wysłać audio.`);
            }
        } else if (dataFromElevenLabs.type === 'close_session' || dataFromElevenLabs.status === 'closed') { // Dodano sprawdzenie statusu 'closed'
            fastify.log.info(`[${callSid}] Agent ElevenLabs zakończył sesję.`);
            connection.socket.close(1000, "Sesja zakończona przez agenta");
            if (elevenLabsSocket && elevenLabsSocket.readyState === WebSocket.OPEN) {
                elevenLabsSocket.close();
            }
            if (fastify.callContexts && fastify.callContexts[callSid]) { // Usuń kontekst po zakończeniu
                delete fastify.callContexts[callSid];
            }
        }
    };

    elevenLabsSocket.onclose = (event) => {
        fastify.log.info(`[${callSid}] Rozłączono z ElevenLabs WebSocket. Kod: ${event.code}, Powód: ${event.reason}`);
        if (connection.socket.readyState === WebSocket.OPEN) {
            connection.socket.close(1000, "Połączenie z agentem AI zakończone.");
        }
        if (fastify.callContexts && fastify.callContexts[callSid]) {
            delete fastify.callContexts[callSid];
        }
    };

    elevenLabsSocket.onerror = (error) => {
        fastify.log.error(`[${callSid}] Błąd ElevenLabs WebSocket: ${error.message}`);
        if (connection.socket.readyState === WebSocket.OPEN) {
            connection.socket.close(1011, "Błąd agenta AI.");
        }
        if (fastify.callContexts && fastify.callContexts[callSid]) {
            delete fastify.callContexts[callSid];
        }
    };

    connection.socket.on('message', message => {
        const msgStr = message.toString();
        const twilioMsg = JSON.parse(msgStr);
        fastify.log.debug(`[${callSid}] Otrzymano wiadomość od Twilio: ${twilioMsg.event}`);

        if (twilioMsg.event === 'start') {
            fastify.log.info(`[${callSid}] Rozmowa rozpoczęta (event: start). Stream SID: ${twilioMsg.start.streamSid}`);
            connection.streamSid = twilioMsg.start.streamSid; 
            // Konfiguracja do agenta ElevenLabs jest teraz wysyłana w elevenLabsSocket.onopen
            // po odzyskaniu kontekstu na początku handlera WebSocket.
        } else if (twilioMsg.event === 'media') {
            if (elevenLabsSocket && elevenLabsSocket.readyState === WebSocket.OPEN) {
                elevenLabsSocket.send(JSON.stringify({
                    audio_base64: twilioMsg.media.payload
                }));
            }
        } else if (twilioMsg.event === 'stop') {
            fastify.log.info(`[${callSid}] Rozmowa zatrzymana przez Twilio (event: stop).`);
            if (elevenLabsSocket && elevenLabsSocket.readyState === WebSocket.OPEN) {
                elevenLabsSocket.send(JSON.stringify({ "type": "close_session" }));
                elevenLabsSocket.close();
            }
             if (fastify.callContexts && fastify.callContexts[callSid]) {
                delete fastify.callContexts[callSid];
            }
        } else if (twilioMsg.event === 'mark') {
            fastify.log.info(`[${callSid}] Otrzymano event 'mark' od Twilio: ${twilioMsg.mark.name}`);
        }
    });

    connection.socket.on('close', (code, reason) => {
        fastify.log.info(`[${callSid}] Połączenie WebSocket od Twilio zostało zamknięte. Kod: ${code}, Powód: ${reason ? reason.toString() : 'Brak powodu'}`);
        if (elevenLabsSocket && elevenLabsSocket.readyState === WebSocket.OPEN) {
            elevenLabsSocket.send(JSON.stringify({ "type": "close_session" }));
            elevenLabsSocket.close();
        }
        if (fastify.callContexts && fastify.callContexts[callSid]) {
            delete fastify.callContexts[callSid];
        }
    });

    connection.socket.on('error', (error) => {
        fastify.log.error(`[${callSid}] Błąd WebSocket od Twilio: ${error.message}`);
        if (elevenLabsSocket && (elevenLabsSocket.readyState === WebSocket.OPEN || elevenLabsSocket.readyState === WebSocket.CONNECTING) ) {
            elevenLabsSocket.close();
        }
        if (fastify.callContexts && fastify.callContexts[callSid]) {
            delete fastify.callContexts[callSid];
        }
    });
});

const start = async () => {
    try {
        await fastify.listen({ port: SERVER_PORT, host: '0.0.0.0' });
        fastify.log.info(`Serwer nasłuchuje na porcie ${SERVER_PORT}`);
    } catch (err) {
        fastify.log.error(err);
        process.exit(1);
    }
};

start();